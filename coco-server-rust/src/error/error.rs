use thiserror::Error;

#[derive(Error, Debug)]
pub enum CocoError {
    #[error("Configuration error: {0}")]
    ConfigError(String),

    #[error("IO error: {0}")]
    IoError(#[from] std::io::Error),

    #[error("YAML parsing error: {0}")]
    YamlError(#[from] serde_yaml::Error),

    #[error("JSON parsing error: {0}")]
    JsonError(#[from] serde_json::Error),

    #[error("Authentication error: {0}")]
    AuthError(String),

    #[error("WebSocket error: {0}")]
    WebSocketError(String),

    #[error("Server error: {0}")]
    ServerError(String),

    #[error("Invalid request: {0}")]
    InvalidRequest(String),

    #[error("Password hashing error: {0}")]
    BcryptError(#[from] bcrypt::BcryptError),

    #[error("Database error: {0}")]
    Database(String),
}

impl CocoError {
    pub fn config(msg: &str) -> Self {
        CocoError::ConfigError(msg.to_string())
    }

    pub fn auth(msg: &str) -> Self {
        CocoError::AuthError(msg.to_string())
    }

    pub fn websocket(msg: &str) -> Self {
        CocoError::WebSocketError(msg.to_string())
    }

    pub fn server(msg: &str) -> Self {
        CocoError::ServerError(msg.to_string())
    }

    pub fn invalid_request(msg: &str) -> Self {
        CocoError::InvalidRequest(msg.to_string())
    }
}
