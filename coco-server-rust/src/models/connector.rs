use serde::{Deserialize, Serialize};
use serde_json::Value;

/// 连接器配置模型
/// 
/// 与Go版本完全兼容的连接器配置结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConnectorConfig {
    /// 连接器ID
    pub id: String,
    
    /// 连接器配置参数
    #[serde(skip_serializing_if = "Option::is_none")]
    pub config: Option<Value>,
}

impl ConnectorConfig {
    /// 创建新的连接器配置
    pub fn new(id: String) -> Self {
        Self {
            id,
            config: None,
        }
    }
    
    /// 设置配置参数
    pub fn with_config(mut self, config: Value) -> Self {
        self.config = Some(config);
        self
    }
}
